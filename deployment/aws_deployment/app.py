#!/usr/bin/env python3
from aws_cdk import App, Fn

from deployment.aws_deployment.stacks.ExtractionStack import ExtractionStack
from deployment.aws_deployment.stacks.NetworkStack import NetworkStack
from deployment.aws_deployment.stacks.OrchestratorStack import OrchestratorStack
from shared.python.utils.get_aws_config import get_aws_config

app = App()

# ************************************************************************************************

aws_config = get_aws_config()
aws_infra_config = aws_config.get("infrastructure", {})
infra_suffix = aws_infra_config.get("infrastructure-suffix")


# ************************************************************************************************

stacks_config = aws_infra_config.get("stacks")


network_config = stacks_config.get("network")
network_stack = NetworkStack(
    scope=app,
    aws_config=network_config,
    deployment_env=aws_config.get("deployment-env"),
)
# Need to get bastion sg id so that orchestrator can enable inbound TCP from it.
bastion_sg_output_id = (
    network_config.get("ec2").get("security-group").get("id") + "-output"
)
bastion_sg_id = Fn.import_value(bastion_sg_output_id)
orchestrator_stack = OrchestratorStack(
    scope=app,
    aws_config=stacks_config.get("prefect"),
    deployment_env=aws_config.get("deployment-env"),
    vpc=network_stack.vpc,
    bastion_sg_id=bastion_sg_id,
    private_hosted_zone=getattr(network_stack, "private_hosted_zone", None),
    region=aws_config.get("region"),
)

# Get orchestrator IP address using the utility functions
orchestrator_instance = getattr(orchestrator_stack, "ec2_instance", None)
orchestrator_ip = orchestrator_instance.instance_private_ip

extraction_stack = ExtractionStack(
    scope=app,
    aws_config=stacks_config.get("extraction"),
    deployment_env=aws_config.get("deployment-env"),
    vpc=network_stack.vpc,
    security_groups=network_stack.security_groups,
    orchestrator_ip=orchestrator_ip,
)

print("End of app.py")

app.synth()

# ************************************************************************************************
# ************************************************************************************************
