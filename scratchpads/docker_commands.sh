#!/bin/bash

docker exec -it "orchestrator-server-1" /bin/bash


# Simple build
# ********************************************************************************

docker build -t extraction-service -f docker/extraction/extraction/Dockerfile .

# Run the container
# Note: You might need to adjust volume mounts or network settings based on your requirements
docker run -it extraction-service
docker run -it --entrypoint /bin/bash extraction-service


# ********************************************************************************

docker build -t devcontainer -f /home/<USER>/repos/betting_exchange/docker/devcontainer/devcontainer/Dockerfile .

docker build -t devcontainer -f /betting_exchange/docker/devcontainer/devcontainer/Dockerfile .
docker build -t devcontainer -f docker/prefect/operational_bootstrap/Dockerfile .
docker run --rm devcontainer node --version
docker run --rm 677276111650.dkr.ecr.us-east-2.amazonaws.com/devcontainer:devcontainer-latest node --version

docker run -it 677276111650.dkr.ecr.us-east-2.amazonaws.com/devcontainer:devcontainer-latest /bin/bash
docker run -it devcontainer /bin/bash

/home/<USER>/repos/betting_exchange/deployment/image_export/build_and_export.sh

aws iam list-users

# ********************************************************************************

aws ecr list-images --repository-name extraction --region us-east-2